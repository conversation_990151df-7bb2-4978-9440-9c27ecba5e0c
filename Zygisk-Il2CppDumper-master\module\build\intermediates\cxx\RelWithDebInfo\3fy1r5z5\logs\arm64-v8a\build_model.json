{"abi": "ARM64_V8A", "info": {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, "cxxBuildFolder": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\.cxx\\RelWithDebInfo\\3fy1r5z5\\arm64-v8a", "soFolder": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\build\\intermediates\\cxx\\RelWithDebInfo\\3fy1r5z5\\obj\\arm64-v8a", "soRepublishFolder": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\build\\intermediates\\cmake\\release\\obj\\arm64-v8a", "abiPlatformVersion": 23, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DMODULE_NAME:STRING=il2cppdumper"], "cFlagsList": [], "cppFlagsList": [], "variantName": "release", "isDebuggableEnabled": false, "validAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\.cxx", "intermediatesBaseFolder": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\build\\intermediates", "intermediatesFolder": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\build\\intermediates\\cxx", "gradleModulePathName": ":module", "moduleRootFolder": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module", "moduleBuildFile": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\build.gradle", "makeFile": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125", "ndkVersion": "26.1.10909125", "ndkSupportedAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultAbiList": ["ARMEABI_V7A", "ARM64_V8A", "X86", "X86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 34, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34}}, "ndkMetaAbiList": [{"abi": "ARMEABI_V7A", "bitness": 32, "deprecated": false, "default": true}, {"abi": "ARM64_V8A", "bitness": 64, "deprecated": false, "default": true}, {"abi": "X86", "bitness": 32, "deprecated": false, "default": true}, {"abi": "X86_64", "bitness": 64, "deprecated": false, "default": true}], "cmakeToolchainFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake", "cmake": {"isValidCmakeAvailable": true, "cmakeExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe", "minimumCmakeVersion": "3.22.1"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"ARMEABI_V7A": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "ARM64_V8A": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "X86": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "X86_64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master", "sdkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": true}, "outputOptions": [], "ninjaExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe"}, "prefabClassPaths": ["C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.0.0\\f2702b5ca13df54e3ca92f29d6b403fb6285d8df\\cli-2.0.0-all.jar"], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "RelWithDebInfo"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\.cxx\\RelWithDebInfo\\3fy1r5z5\\prefab\\arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "3fy1r5z502t25s313q1l1jf2t6o67504om4l3m27d4w60536l5r133k6q57", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 7.4.2.\n#   - $NDK is the path to NDK 26.1.10909125.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-H$PROJECT/module/src/main/cpp\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=23\n-DANDROID_PLATFORM=android-23\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=$PROJECT/module/build/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=$PROJECT/module/build/intermediates/cxx/RelWithDebInfo/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=RelWithDebInfo\n-DCMAKE_FIND_ROOT_PATH=$PROJECT/module/.cxx/RelWithDebInfo/$HASH/prefab/$ABI/prefab\n-B$PROJECT/module/.cxx/RelWithDebInfo/$HASH/$ABI\n-GNinja\n-DMODULE_NAME:STRING=il2cppdumper", "configurationArguments": ["-HC:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=23", "-DANDROID_PLATFORM=android-23", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125", "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125", "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\build\\intermediates\\cxx\\RelWithDebInfo\\3fy1r5z5\\obj\\arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\build\\intermediates\\cxx\\RelWithDebInfo\\3fy1r5z5\\obj\\arm64-v8a", "-DCMAKE_BUILD_TYPE=RelWithDebInfo", "-DCMAKE_FIND_ROOT_PATH=C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\.cxx\\RelWithDebInfo\\3fy1r5z5\\prefab\\arm64-v8a\\prefab", "-BC:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\.cxx\\RelWithDebInfo\\3fy1r5z5\\arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>", "-DMODULE_NAME:STRING=il2cppdumper"], "intermediatesParentFolder": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\build\\intermediates\\cxx\\RelWithDebInfo\\3fy1r5z5"}