                        -HC:\Users\<USER>\Downloads\Zygisk-Il2CppDumper-master\Zygisk-Il2CppDumper-master\module\src\main\cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-D<PERSON>DROID_PLATFORM=android-23
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\26.1.10909125\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\Downloads\Zygisk-Il2CppDumper-master\Zygisk-Il2CppDumper-master\module\build\intermediates\cxx\RelWithDebInfo\3fy1r5z5\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\Downloads\Zygisk-Il2CppDumper-master\Zygisk-Il2CppDumper-master\module\build\intermediates\cxx\RelWithDebInfo\3fy1r5z5\obj\x86
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-DCMAKE_FIND_ROOT_PATH=C:\Users\<USER>\Downloads\Zygisk-Il2CppDumper-master\Zygisk-Il2CppDumper-master\module\.cxx\RelWithDebInfo\3fy1r5z5\prefab\x86\prefab
-BC:\Users\<USER>\Downloads\Zygisk-Il2CppDumper-master\Zygisk-Il2CppDumper-master\module\.cxx\RelWithDebInfo\3fy1r5z5\x86
-GNinja
-DMODULE_NAME:STRING=il2cppdumper
                        Build command args: []
                        Version: 2