[{"directory": "C:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/.cxx/RelWithDebInfo/3fy1r5z5/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android23 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dil2cppdumper_EXPORTS -IC:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/src/main/cpp/xdl/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -mstackrealign -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -Werror=format -fdata-sections -ffunction-sections -O2 -fvisibility=hidden -fvisibility-inlines-hidden  -fno-exceptions -fno-rtti -O2 -g -DNDEBUG -fPIC -std=gnu++20 -o CMakeFiles\\il2cppdumper.dir\\main.cpp.o -c C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\main.cpp", "file": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\main.cpp"}, {"directory": "C:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/.cxx/RelWithDebInfo/3fy1r5z5/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android23 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dil2cppdumper_EXPORTS -IC:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/src/main/cpp/xdl/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -mstackrealign -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -Werror=format -fdata-sections -ffunction-sections -O2 -fvisibility=hidden -fvisibility-inlines-hidden  -fno-exceptions -fno-rtti -O2 -g -DNDEBUG -fPIC -std=gnu++20 -o CMakeFiles\\il2cppdumper.dir\\hack.cpp.o -c C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\hack.cpp", "file": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\hack.cpp"}, {"directory": "C:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/.cxx/RelWithDebInfo/3fy1r5z5/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=i686-none-linux-android23 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dil2cppdumper_EXPORTS -IC:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/src/main/cpp/xdl/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -mstackrealign -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -Werror=format -fdata-sections -ffunction-sections -O2 -fvisibility=hidden -fvisibility-inlines-hidden  -fno-exceptions -fno-rtti -O2 -g -DNDEBUG -fPIC -std=gnu++20 -o CMakeFiles\\il2cppdumper.dir\\il2cpp_dump.cpp.o -c C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\il2cpp_dump.cpp", "file": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\il2cpp_dump.cpp"}, {"directory": "C:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/.cxx/RelWithDebInfo/3fy1r5z5/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=i686-none-linux-android23 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dil2cppdumper_EXPORTS -IC:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/src/main/cpp/xdl/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -mstackrealign -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Werror=format -fdata-sections -ffunction-sections -O2 -fvisibility=hidden -fvisibility-inlines-hidden -O2 -g -DNDEBUG -fPIC -o CMakeFiles\\il2cppdumper.dir\\xdl\\xdl.c.o -c C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\xdl\\xdl.c", "file": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\xdl\\xdl.c"}, {"directory": "C:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/.cxx/RelWithDebInfo/3fy1r5z5/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=i686-none-linux-android23 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dil2cppdumper_EXPORTS -IC:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/src/main/cpp/xdl/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -mstackrealign -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Werror=format -fdata-sections -ffunction-sections -O2 -fvisibility=hidden -fvisibility-inlines-hidden -O2 -g -DNDEBUG -fPIC -o CMakeFiles\\il2cppdumper.dir\\xdl\\xdl_iterate.c.o -c C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\xdl\\xdl_iterate.c", "file": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\xdl\\xdl_iterate.c"}, {"directory": "C:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/.cxx/RelWithDebInfo/3fy1r5z5/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=i686-none-linux-android23 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dil2cppdumper_EXPORTS -IC:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/src/main/cpp/xdl/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -mstackrealign -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Werror=format -fdata-sections -ffunction-sections -O2 -fvisibility=hidden -fvisibility-inlines-hidden -O2 -g -DNDEBUG -fPIC -o CMakeFiles\\il2cppdumper.dir\\xdl\\xdl_linker.c.o -c C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\xdl\\xdl_linker.c", "file": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\xdl\\xdl_linker.c"}, {"directory": "C:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/.cxx/RelWithDebInfo/3fy1r5z5/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=i686-none-linux-android23 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dil2cppdumper_EXPORTS -IC:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/src/main/cpp/xdl/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -mstackrealign -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Werror=format -fdata-sections -ffunction-sections -O2 -fvisibility=hidden -fvisibility-inlines-hidden -O2 -g -DNDEBUG -fPIC -o CMakeFiles\\il2cppdumper.dir\\xdl\\xdl_lzma.c.o -c C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\xdl\\xdl_lzma.c", "file": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\xdl\\xdl_lzma.c"}, {"directory": "C:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/.cxx/RelWithDebInfo/3fy1r5z5/x86", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\26.1.10909125\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe --target=i686-none-linux-android23 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dil2cppdumper_EXPORTS -IC:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/src/main/cpp/xdl/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -mstackrealign -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Werror=format -fdata-sections -ffunction-sections -O2 -fvisibility=hidden -fvisibility-inlines-hidden -O2 -g -DNDEBUG -fPIC -o CMakeFiles\\il2cppdumper.dir\\xdl\\xdl_util.c.o -c C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\xdl\\xdl_util.c", "file": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\xdl\\xdl_util.c"}]