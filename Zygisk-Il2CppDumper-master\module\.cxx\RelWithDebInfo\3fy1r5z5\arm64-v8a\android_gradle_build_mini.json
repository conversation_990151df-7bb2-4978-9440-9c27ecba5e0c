{"buildFiles": ["C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\src\\main\\cpp\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\.cxx\\RelWithDebInfo\\3fy1r5z5\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\.cxx\\RelWithDebInfo\\3fy1r5z5\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"il2cppdumper::@6890427a1f51a3e7e1df": {"artifactName": "il2cppdumper", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Downloads\\Zygisk-Il2CppDumper-master\\Zygisk-Il2CppDumper-master\\module\\build\\intermediates\\cxx\\RelWithDebInfo\\3fy1r5z5\\obj\\arm64-v8a\\libil2cppdumper.so", "runtimeFiles": []}}}