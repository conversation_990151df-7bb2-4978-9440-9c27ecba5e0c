# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 25ms]
    create-X86_64-model 20ms
    [gap of 16ms]
    create-ARM64_V8A-model 10ms
    [gap of 13ms]
  create-initial-cxx-model completed in 84ms
create_cxx_tasks completed in 87ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 58ms]
    create-X86_64-model 23ms
  create-initial-cxx-model completed in 81ms
create_cxx_tasks completed in 84ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 112ms
    create-module-model completed in 117ms
    [gap of 20ms]
    create-X86_64-model 22ms
  create-initial-cxx-model completed in 189ms
create_cxx_tasks completed in 191ms

