#!/system/bin/sh

echo "=== Il2CppDumper Debug Script ==="
echo "Date: $(date)"
echo ""

echo "1. Checking Magisk status..."
if [ -f /data/adb/magisk/magisk ]; then
    echo "✓ Magisk found"
    /data/adb/magisk/magisk --version
else
    echo "✗ Magisk not found"
fi
echo ""

echo "2. Checking Zygisk status..."
if [ -f /data/adb/magisk/zygisk ]; then
    echo "✓ Zygisk binary found"
else
    echo "✗ Zygisk binary not found"
fi
echo ""

echo "3. Checking module installation..."
if [ -d /data/adb/modules/zygisk_il2cppdumper ]; then
    echo "✓ Module directory found"
    ls -la /data/adb/modules/zygisk_il2cppdumper/
else
    echo "✗ Module directory not found"
fi
echo ""

echo "4. Checking module files..."
if [ -f /data/adb/modules/zygisk_il2cppdumper/zygisk/arm64-v8a.so ]; then
    echo "✓ Module library found"
    ls -la /data/adb/modules/zygisk_il2cppdumper/zygisk/
else
    echo "✗ Module library not found"
fi
echo ""

echo "5. Checking Free Fire installation..."
pm list packages | grep -i fire
echo ""

echo "6. Checking Free Fire process..."
ps | grep -i fire
echo ""

echo "7. Checking for dump files..."
find /data/data -name "*dump*" 2>/dev/null
find /data/data -name "*.cs" 2>/dev/null
echo ""

echo "8. Checking logcat for il2cpp messages..."
logcat -d | grep -i il2cpp | tail -20
echo ""

echo "9. Checking logcat for dumper messages..."
logcat -d | grep -i dumper | tail -10
echo ""

echo "=== Debug Complete ==="
