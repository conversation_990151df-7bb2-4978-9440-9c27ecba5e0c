{"artifacts": [{"path": "C:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/build/intermediates/cxx/RelWithDebInfo/3fy1r5z5/obj/x86/libil2cppdumper.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 36, "parent": 0}, {"command": 1, "file": 0, "line": 41, "parent": 0}, {"command": 2, "file": 0, "line": 30, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -mstackrealign -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -Werror=format -fdata-sections -ffunction-sections -O2 -fvisibility=hidden -fvisibility-inlines-hidden  -fno-exceptions -fno-rtti -O2 -g -DNDEBUG -fPIC"}, {"fragment": "-std=gnu++20"}], "defines": [{"define": "il2cppdumper_EXPORTS"}], "includes": [{"backtrace": 3, "path": "C:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/src/main/cpp/xdl/include"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "20"}, "sourceIndexes": [0, 1, 2], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, {"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -mstackrealign -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security  -Werror=format -fdata-sections -ffunction-sections -O2 -fvisibility=hidden -fvisibility-inlines-hidden -O2 -g -DNDEBUG -fPIC"}], "defines": [{"define": "il2cppdumper_EXPORTS"}], "includes": [{"backtrace": 3, "path": "C:/Users/<USER>/Downloads/Zygisk-Il2CppDumper-master/Zygisk-Il2CppDumper-master/module/src/main/cpp/xdl/include"}], "language": "C", "sourceIndexes": [3, 4, 5, 6, 7], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "il2cppdumper::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-static-libstdc++ -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments  -ffixed-x18 -Wl,--hash-style=both -Wl,-exclude-libs,ALL -Wl,--gc-sections -Wl,--strip-all -Wl,--gc-sections", "role": "flags"}, {"backtrace": 2, "fragment": "-llog", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "il2cppdumper", "nameOnDisk": "libil2cppdumper.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "hack.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "il2cpp_dump.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "xdl/xdl.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "xdl/xdl_iterate.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "xdl/xdl_linker.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "xdl/xdl_lzma.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 1, "path": "xdl/xdl_util.c", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}