import org.apache.tools.ant.filters.FixCrLfFilter

import java.nio.file.Paths
import java.nio.file.Files

apply plugin: 'com.android.library'
apply from: file(rootProject.file('module.gradle'))

android {
    compileSdkVersion rootProject.ext.targetSdkVersion
    ndkVersion '26.1.10909125'
    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        externalNativeBuild {
            cmake {
                arguments "-DMODULE_NAME:STRING=$moduleLibraryName"
            }
        }
    }
    buildFeatures {
        prefab true
    }
    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.22.1"
        }
    }
}

repositories {
    mavenLocal()
}

afterEvaluate {
    android.libraryVariants.forEach { variant ->
        def variantCapped = variant.name.capitalize()
        def variantLowered = variant.name.toLowerCase()

        def zipName = "${magiskModuleId.replace('_', '-')}-${moduleVersion}-${variantLowered}.zip"
        def magiskDir = file("$outDir/magisk_module_$variantLowered")

        task("prepareMagiskFiles${variantCapped}", type: Sync) {
            dependsOn("assemble$variantCapped")

            def templatePath = "$rootDir/template/magisk_module"

            into magiskDir
            from(templatePath) {
                exclude 'module.prop'
            }
            from(templatePath) {
                include 'module.prop'
                expand([
                        id         : magiskModuleId,
                        name       : moduleName,
                        version    : moduleVersion,
                        versionCode: moduleVersionCode.toString(),
                        author     : moduleAuthor,
                        description: moduleDescription,
                ])
                filter(FixCrLfFilter.class,
                        eol: FixCrLfFilter.CrLf.newInstance("lf"))
            }
            from("$buildDir/intermediates/stripped_native_libs/$variantLowered/out/lib") {
                into 'lib'
            }
            doLast {
                file("$magiskDir/zygisk").mkdir()
                fileTree("$magiskDir/lib").visit { f ->
                    if (!f.directory) return
                    def srcPath = Paths.get("${f.file.absolutePath}/lib${moduleLibraryName}.so")
                    def dstPath = Paths.get("$magiskDir/zygisk/${f.path}.so")
                    Files.move(srcPath, dstPath)
                }
                new File("$magiskDir/lib").deleteDir()
            }
        }

        task("zip${variantCapped}", type: Zip) {
            dependsOn("prepareMagiskFiles${variantCapped}")
            from magiskDir
            archiveFileName.set(zipName)
            destinationDirectory.set(outDir)
        }

        task("push${variantCapped}", type: Exec) {
            dependsOn("zip${variantCapped}")
            workingDir outDir
            commandLine android.adbExecutable, "push", zipName, "/data/local/tmp/"
        }

        task("flash${variantCapped}", type: Exec) {
            dependsOn("push${variantCapped}")
            commandLine android.adbExecutable, "shell", "su", "-c",
                    "magisk --install-module /data/local/tmp/${zipName}"
        }

        task("flashAndReboot${variantCapped}", type: Exec) {
            dependsOn("flash${variantCapped}")
            commandLine android.adbExecutable, "shell", "reboot"
        }

        variant.assembleProvider.get().finalizedBy("zip${variantCapped}")
    }
}
