[{"level_": 0, "message_": "android.ndkVersion from module build.gradle is [26.1.10909125]", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1371212061}, {"level_": 0, "message_": "android.ndkPath from module build.gradle is not set", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -518946967}, {"level_": 0, "message_": "ndk.dir in local.properties is C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/26.1.10909125", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -820791385}, {"level_": 0, "message_": "Not considering ANDROID_NDK_HOME because support was removed after deprecation period.", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 2048328296}, {"level_": 0, "message_": "sdkFolder is C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 2064169989}, {"level_": 0, "message_": "Checking whether deleting ndk.dir and setting android.ndkVersion to [26.1.10909125] would result in the same NDK", "file_": "", "tag_": "", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -44677488}, {"level_": 0, "message_": "android.ndkVersion from module build.gradle is [26.1.10909125]", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1747485538}, {"level_": 0, "message_": "android.ndkPath from module build.gradle is not set", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -895220444}, {"level_": 0, "message_": "ndk.dir in local.properties is not set", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -190759251}, {"level_": 0, "message_": "Not considering ANDROID_NDK_HOME because support was removed after deprecation period.", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1672054819}, {"level_": 0, "message_": "sdkFolder is C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": 1687896512}, {"level_": 0, "message_": "Deleting ndk.dir and setting android.ndkVersion to [26.1.10909125] would result in the same NDK.", "file_": "", "tag_": "ndk.dir delete check", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -1416083796}, {"level_": 2, "message_": "NDK was located by using ndk.dir property. This method is deprecated and will be removed in a future release. Please delete ndk.dir from local.properties and set android.ndkVersion to [26.1.10909125] in all native modules in the project. https://developer.android.com/r/studio-ui/ndk-dir", "file_": "", "tag_": "", "diagnosticCode_": 5106, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}, "fieldsDescending": {}}, "memoizedSize": -1, "memoizedHashCode": -722294656}]