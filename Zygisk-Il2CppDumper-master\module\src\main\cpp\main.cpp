#include <cstring>
#include <thread>
#include <fcntl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>
#include <cinttypes>
#include "hack.h"
#include "zygisk.hpp"
#include "game.h"
#include "log.h"

using zygisk::Api;
using zygisk::AppSpecializeArgs;
using zygisk::ServerSpecializeArgs;

class MyModule : public zygisk::ModuleBase {
public:
    void onLoad(Api *api, JNIEnv *env) override {
        this->api = api;
        this->env = env;
    }

    void preAppSpecialize(AppSpecializeArgs *args) override {
        auto package_name = env->GetStringUTFChars(args->nice_name, nullptr);
        auto app_data_dir = env->GetStringUTFChars(args->app_data_dir, nullptr);
        preSpecialize(package_name, app_data_dir);
        env->ReleaseStringUTFChars(args->nice_name, package_name);
        env->ReleaseStringUTFChars(args->app_data_dir, app_data_dir);
    }

    void postAppSpecialize(const AppSpecializeArgs *) override {
        LOGI("postAppSpecialize called, enable_hack: %s", enable_hack ? "true" : "false");
        if (enable_hack) {
            LOGI("✓ Starting hack thread for game data dir: %s", game_data_dir);
            std::thread hack_thread(hack_prepare, game_data_dir, data, length);
            hack_thread.detach();
            LOGI("✓ Hack thread started and detached");
        } else {
            LOGI("✗ Hack not enabled, skipping");
        }
    }

private:
    Api *api;
    JNIEnv *env;
    bool enable_hack;
    char *game_data_dir;
    void *data;
    size_t length;

    void preSpecialize(const char *package_name, const char *app_data_dir) {
        LOGI("Checking package: %s", package_name);
        LOGI("Target packages: %s, %s, %s, %s", GamePackageName, GamePackageName2, GamePackageName3, GamePackageName4);

        if (strcmp(package_name, GamePackageName) == 0 ||
            strcmp(package_name, GamePackageName2) == 0 ||
            strcmp(package_name, GamePackageName3) == 0 ||
            strcmp(package_name, GamePackageName4) == 0) {
            LOGI("✅ TARGET GAME DETECTED: %s", package_name);
            LOGI("✅ App data dir: %s", app_data_dir);
            enable_hack = true;
            game_data_dir = new char[strlen(app_data_dir) + 1];
            strcpy(game_data_dir, app_data_dir);
            LOGI("✅ Game data dir saved: %s", game_data_dir);

#if defined(__i386__)
            auto path = "zygisk/armeabi-v7a.so";
#endif
#if defined(__x86_64__)
            auto path = "zygisk/arm64-v8a.so";
#endif
#if defined(__i386__) || defined(__x86_64__)
            int dirfd = api->getModuleDir();
            int fd = openat(dirfd, path, O_RDONLY);
            if (fd != -1) {
                struct stat sb{};
                fstat(fd, &sb);
                length = sb.st_size;
                data = mmap(nullptr, length, PROT_READ, MAP_PRIVATE, fd, 0);
                close(fd);
            } else {
                LOGW("Unable to open arm file");
            }
#endif
        } else {
            api->setOption(zygisk::Option::DLCLOSE_MODULE_LIBRARY);
        }
    }
};

REGISTER_ZYGISK_MODULE(MyModule)