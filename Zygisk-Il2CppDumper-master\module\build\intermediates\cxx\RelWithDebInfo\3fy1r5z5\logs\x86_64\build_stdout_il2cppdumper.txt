ninja: Entering directory `C:\Users\<USER>\Downloads\Zygisk-Il2CppDumper-master\Zygisk-Il2CppDumper-master\module\.cxx\RelWithDebInfo\3fy1r5z5\x86_64'
[1/9] Building C object CMakeFiles/il2cppdumper.dir/xdl/xdl_lzma.c.o
[2/9] Building C object CMakeFiles/il2cppdumper.dir/xdl/xdl_linker.c.o
[3/9] Building C object CMakeFiles/il2cppdumper.dir/xdl/xdl_util.c.o
[4/9] Building C object CMakeFiles/il2cppdumper.dir/xdl/xdl.c.o
[5/9] Building C object CMakeFiles/il2cppdumper.dir/xdl/xdl_iterate.c.o
[6/9] Building CXX object CMakeFiles/il2cppdumper.dir/main.cpp.o
[7/9] Building CXX object CMakeFiles/il2cppdumper.dir/hack.cpp.o
[8/9] Building CXX object CMakeFiles/il2cppdumper.dir/il2cpp_dump.cpp.o
[9/9] Linking CXX shared library C:\Users\<USER>\Downloads\Zygisk-Il2CppDumper-master\Zygisk-Il2CppDumper-master\module\build\intermediates\cxx\RelWithDebInfo\3fy1r5z5\obj\x86_64\libil2cppdumper.so
